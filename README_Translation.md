# مترجم انگلیسی به فارسی با Transformer

این پروژه یک مترجم انگلیسی به فارسی با استفاده از معماری Transformer پیاده‌سازی شده است.

## نحوه استفاده

### 1. آموزش مدل (اولین بار)

```bash
python "MT+transformer.py" 1
```

یا

```bash
python "MT+transformer.py" train
```

### 2. ترجمه تعاملی

```bash
python translate.py
```

یا

```bash
python "MT+transformer.py" 2
```

یا

```bash
python "MT+transformer.py" interactive
```

### 3. ترجمه یک جمله

```bash
python translate.py "Hello world"
```

یا

```bash
python "MT+transformer.py" 3 "Hello world"
```

یا

```bash
python "MT+transformer.py" single "Hello world"
```

### 4. استفاده از فایل batch (ویندوز)

```cmd
translate.bat
```

یا

```cmd
translate.bat "Hello world"
```

## مثال‌های استفاده

### حالت تعاملی:
```
python translate.py

=== مترجم انگلیسی به فارسی ===
برای خروج 'quit' یا 'exit' تایپ کنید
----------------------------------------

جمله انگلیسی را وارد کنید: Hello world
در حال ترجمه...
انگلیسی: Hello world
فارسی: سلام دنیا
----------------------------------------

جمله انگلیسی را وارد کنید: quit
خداحافظ!
```

### ترجمه مستقیم:
```
python translate.py "I love programming"

جمله ورودی: I love programming
در حال ترجمه...
ترجمه: من برنامه نویسی را دوست دارم
```

## ساختار فایل‌ها

- `MT+transformer.py` - فایل اصلی حاوی مدل و توابع آموزش
- `translate.py` - اسکریپت ساده برای ترجمه
- `translate.bat` - فایل batch برای ویندوز
- `dataset.tsv` - فایل دیتاست (انگلیسی-فارسی)
- `models/` - پوشه ذخیره مدل‌های آموزش دیده

## نیازمندی‌ها

- Python 3.7+
- PyTorch
- NumPy
- tqdm

## نصب نیازمندی‌ها

```bash
pip install torch numpy tqdm
```

## نکات مهم

1. ابتدا باید مدل را آموزش دهید (گزینه 1)
2. فایل دیتاست باید در مسیر صحیح قرار داشته باشد
3. مدل آموزش دیده در پوشه `models/` ذخیره می‌شود
4. برای بهترین نتایج، از جملات ساده و کوتاه استفاده کنید

## عیب‌یابی

اگر خطای "مدل یافت نشد" دریافت کردید:
1. ابتدا مدل را آموزش دهید
2. مطمئن شوید پوشه `models/` و فایل مدل موجود است
3. مسیر فایل دیتاست را بررسی کنید
