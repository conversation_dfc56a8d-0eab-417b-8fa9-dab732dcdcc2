#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت ساده برای ترجمه جملات با مدل Transformer
"""

import sys
import os

# اضافه کردن مسیر فایل اصلی
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from MT_transformer import translate_single_sentence, interactive_translate, load_model_and_dataset
except ImportError:
    # اگر نام فایل متفاوت باشد
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("mt_module", "MT+transformer.py")
        mt_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mt_module)
        translate_single_sentence = mt_module.translate_single_sentence
        interactive_translate = mt_module.interactive_translate
        load_model_and_dataset = mt_module.load_model_and_dataset
    except Exception as e:
        print(f"خطا در بارگذاری ماژول: {e}")
        print("لطفا مطمئن شوید که فایل MT+transformer.py در همین پوشه موجود است.")
        sys.exit(1)


def main():
    """تابع اصلی"""
    if len(sys.argv) == 1:
        # حالت تعاملی
        print("=== مترجم انگلیسی به فارسی ===")
        print("حالت تعاملی - برای خروج 'quit' تایپ کنید")
        interactive_translate()
    
    elif sys.argv[1] in ['-h', '--help', 'help']:
        # راهنما
        print("=== راهنمای استفاده ===")
        print("python translate.py                    # حالت تعاملی")
        print("python translate.py \"Hello world\"      # ترجمه یک جمله")
        print("python translate.py -h                 # نمایش راهنما")
        print("\nمثال:")
        print("python translate.py \"I love programming\"")
    
    else:
        # ترجمه جمله ورودی
        sentence = " ".join(sys.argv[1:])
        print(f"جمله ورودی: {sentence}")
        print("در حال ترجمه...")
        
        translation = translate_single_sentence(sentence)
        print(f"ترجمه: {translation}")


if __name__ == "__main__":
    main()
