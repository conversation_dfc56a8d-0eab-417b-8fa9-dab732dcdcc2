import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import math, csv, os
from collections import Counter
from tqdm import tqdm

# ----------------------------
# Dataset
# ----------------------------
class TranslationDataset(Dataset):
    def __init__(self, src_texts, tgt_texts, src_vocab, tgt_vocab, max_len=64):
        self.src_texts = src_texts
        self.tgt_texts = tgt_texts
        self.src_vocab = src_vocab
        self.tgt_vocab = tgt_vocab
        self.max_len = max_len

        # word2id
        self.src_word2id = {w: i for i, w in enumerate(src_vocab)}
        self.tgt_word2id = {w: i for i, w in enumerate(tgt_vocab)}

        for sp in ["<PAD>", "<UNK>", "<SOS>", "<EOS>"]:
            if sp not in self.src_word2id:
                self.src_word2id[sp] = len(self.src_word2id)
            if sp not in self.tgt_word2id:
                self.tgt_word2id[sp] = len(self.tgt_word2id)

        self.src_id2word = {i: w for w, i in self.src_word2id.items()}
        self.tgt_id2word = {i: w for w, i in self.tgt_word2id.items()}

    def tokenize(self, text): return text.strip().lower().split()

    def encode(self, text, vocab, add_special=True):
        tokens = self.tokenize(text)
        ids = []
        if add_special: ids.append(vocab["<SOS>"])
        for tok in tokens[: self.max_len - 2]:
            ids.append(vocab.get(tok, vocab["<UNK>"]))
        if add_special: ids.append(vocab["<EOS>"])
        while len(ids) < self.max_len:
            ids.append(vocab["<PAD>"])
        return ids[: self.max_len]

    def __len__(self): return len(self.src_texts)

    def __getitem__(self, idx):
        src = self.encode(self.src_texts[idx], self.src_word2id)
        tgt = self.encode(self.tgt_texts[idx], self.tgt_word2id)
        return torch.tensor(src), torch.tensor(tgt)


# ----------------------------
# Attention & Transformer
# ----------------------------
class MultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        self.Wq = nn.Linear(d_model, d_model)
        self.Wk = nn.Linear(d_model, d_model)
        self.Wv = nn.Linear(d_model, d_model)
        self.Wo = nn.Linear(d_model, d_model)

    def forward(self, Q, K, V, mask=None):
        B = Q.size(0)
        Q = self.Wq(Q).view(B, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.Wk(K).view(B, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.Wv(V).view(B, -1, self.num_heads, self.d_k).transpose(1, 2)

        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        if mask is not None:
            scores = scores.masked_fill(~mask, -1e9)

        attn = torch.softmax(scores, dim=-1)
        out = torch.matmul(attn, V)
        out = out.transpose(1, 2).contiguous().view(B, -1, self.d_model)
        return self.Wo(out)


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        pos = torch.arange(0, max_len).unsqueeze(1).float()
        div = torch.exp(torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model))
        pe[:, 0::2], pe[:, 1::2] = torch.sin(pos * div), torch.cos(pos * div)
        self.register_buffer("pe", pe.unsqueeze(0))

    def forward(self, x): return x + self.pe[:, : x.size(1)]


class TransformerBlock(nn.Module):
    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):
        super().__init__()
        self.attn = MultiHeadAttention(d_model, num_heads)
        self.norm1 = nn.LayerNorm(d_model)
        self.ff = nn.Sequential(nn.Linear(d_model, d_ff), nn.ReLU(), nn.Linear(d_ff, d_model))
        self.norm2 = nn.LayerNorm(d_model)
        self.drop = nn.Dropout(dropout)

    def forward(self, x, mask=None):
        x = self.norm1(x + self.drop(self.attn(x, x, x, mask)))
        x = self.norm2(x + self.drop(self.ff(x)))
        return x


class Transformer(nn.Module):
    def __init__(self, src_vocab, tgt_vocab, d_model=128, heads=4, layers=2, d_ff=256, max_len=64):
        super().__init__()
        self.d_model = d_model
        self.src_emb = nn.Embedding(src_vocab, d_model)
        self.tgt_emb = nn.Embedding(tgt_vocab, d_model)
        self.pos = PositionalEncoding(d_model, max_len)
        self.enc_layers = nn.ModuleList([TransformerBlock(d_model, heads, d_ff) for _ in range(layers)])
        self.dec_layers = nn.ModuleList([TransformerBlock(d_model, heads, d_ff) for _ in range(layers)])
        self.out = nn.Linear(d_model, tgt_vocab)

    def make_pad_mask(self, seq, pad=0): return (seq != pad).unsqueeze(1).unsqueeze(2).bool()

    def make_look_ahead(self, size, device):
        mask = torch.triu(torch.ones(size, size, device=device), 1).bool()
        return ~mask

    def encode(self, src, src_mask):
        x = self.pos(self.src_emb(src) * math.sqrt(self.d_model))
        for l in self.enc_layers: x = l(x, src_mask)
        return x

    def decode(self, tgt, enc_out, tgt_mask):
        x = self.pos(self.tgt_emb(tgt) * math.sqrt(self.d_model))
        for l in self.dec_layers: x = l(x, tgt_mask)
        return x

    def forward(self, src, tgt):
        src_mask = self.make_pad_mask(src)
        tgt_mask = self.make_pad_mask(tgt) & self.make_look_ahead(tgt.size(1), tgt.device)
        enc_out = self.encode(src, src_mask)
        dec_out = self.decode(tgt, enc_out, tgt_mask)
        return self.out(dec_out)


# ----------------------------
# Train & Translate
# ----------------------------
def train_loop(model, data, opt, loss_fn, device, epochs=10, save_dir="models"):
    os.makedirs(save_dir, exist_ok=True)
    model.train()
    for ep in range(epochs):
        total = 0
        for src, tgt in tqdm(data, desc=f"Epoch {ep+1}/{epochs}"):
            src, tgt = src.to(device), tgt.to(device)
            opt.zero_grad()
            out = model(src, tgt[:, :-1])
            loss = loss_fn(out.reshape(-1, out.size(-1)), tgt[:, 1:].reshape(-1))
            loss.backward(); opt.step()
            total += loss.item()
        print(f"Epoch {ep+1} Loss: {total/len(data):.4f}")
        torch.save(model.state_dict(), f"{save_dir}/model_epoch{ep+1}.pth")


def translate(model, sentence, dataset, device, max_len=20):
    model.eval()
    src_ids = dataset.encode(sentence, dataset.src_word2id)
    src = torch.tensor([src_ids], device=device)
    tgt = torch.tensor([[dataset.tgt_word2id["<SOS>"]]], device=device)

    with torch.no_grad():
        for _ in range(max_len):
            out = model(src, tgt)
            next_id = out[0, -1].argmax().item()
            tgt = torch.cat([tgt, torch.tensor([[next_id]], device=device)], dim=1)
            if next_id == dataset.tgt_word2id["<EOS>"]: break

    return " ".join([dataset.tgt_id2word[i.item()] for i in tgt[0][1:-1]])


def load_model_and_dataset(model_path="models/model_epoch10.pth", dataset_path="g:/dars 403-02/NLP/project/MT/dataset.tsv"):
    """بارگذاری مدل آموزش دیده و دیتاست برای ترجمه"""

    # بارگذاری دیتاست
    en, fa = [], []
    with open(dataset_path, encoding="utf-8") as f:
        reader = csv.reader(f, delimiter="\t")
        for row in reader:
            if len(row) >= 2:
                en.append(row[0]); fa.append(row[1])
    en, fa = en[:10000], fa[:10000]

    # ساخت واژگان
    en_vocab = [w for w, c in Counter(" ".join(en).lower().split()).items() if c >= 1]
    fa_vocab = [w for w, c in Counter(" ".join(fa).split()).items() if c >= 1]

    dataset = TranslationDataset(en, fa, en_vocab, fa_vocab)

    # بارگذاری مدل
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = Transformer(len(dataset.src_word2id), len(dataset.tgt_word2id)).to(device)

    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"مدل از {model_path} بارگذاری شد")
    else:
        print(f"فایل مدل {model_path} یافت نشد. لطفا ابتدا مدل را آموزش دهید.")
        return None, None, None

    return model, dataset, device


def interactive_translate():
    """تابع تعاملی برای ترجمه جملات ورودی"""
    print("بارگذاری مدل و دیتاست...")
    model, dataset, device = load_model_and_dataset()

    if model is None:
        return

    print("\n=== مترجم انگلیسی به فارسی ===")
    print("برای خروج 'quit' یا 'exit' تایپ کنید")
    print("-" * 40)

    while True:
        try:
            # دریافت جمله ورودی از کاربر
            sentence = input("\nجمله انگلیسی را وارد کنید: ").strip()

            # بررسی خروج
            if sentence.lower() in ['quit', 'exit', 'q']:
                print("خداحافظ!")
                break

            if not sentence:
                print("لطفا جمله‌ای وارد کنید.")
                continue

            # ترجمه
            print("در حال ترجمه...")
            translation = translate(model, sentence, dataset, device)

            print(f"انگلیسی: {sentence}")
            print(f"فارسی: {translation}")
            print("-" * 40)

        except KeyboardInterrupt:
            print("\n\nخداحافظ!")
            break
        except Exception as e:
            print(f"خطا در ترجمه: {e}")


def translate_single_sentence(sentence, model_path="models/model_epoch10.pth"):
    """ترجمه یک جمله واحد"""
    model, dataset, device = load_model_and_dataset(model_path)

    if model is None:
        return "خطا: مدل بارگذاری نشد"

    try:
        translation = translate(model, sentence, dataset, device)
        return translation
    except Exception as e:
        return f"خطا در ترجمه: {e}"


# ----------------------------
# Main
# ----------------------------
def train_model():
    """آموزش مدل ترجمه"""
    # مسیر دیتاست TSV
    file_path = "g:/dars 403-02/NLP/project/MT/dataset.tsv"

    en, fa = [], []
    with open(file_path, encoding="utf-8") as f:
        reader = csv.reader(f, delimiter="\t")
        for row in reader:
            if len(row) >= 2:
                en.append(row[0]); fa.append(row[1])
    en, fa = en[:10000], fa[:10000]  # داده بیشتر

    # ساخت واژگان (c >= 1 یعنی همه کلمات می‌مونن)
    en_vocab = [w for w, c in Counter(" ".join(en).lower().split()).items() if c >= 1]
    fa_vocab = [w for w, c in Counter(" ".join(fa).split()).items() if c >= 1]

    dataset = TranslationDataset(en, fa, en_vocab, fa_vocab)
    loader = DataLoader(dataset, batch_size=32, shuffle=True)

    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = Transformer(len(dataset.src_word2id), len(dataset.tgt_word2id)).to(device)
    opt = optim.Adam(model.parameters(), lr=1e-3)
    loss_fn = nn.CrossEntropyLoss(ignore_index=dataset.tgt_word2id["<PAD>"])

    # آموزش (ذخیره مدل بعد از هر اپوک)
    train_loop(model, loader, opt, loss_fn, device, epochs=10)

    # تست ترجمه
    print("\nTranslate Test:")
    print("EN:", en[0])
    print("FA:", translate(model, en[0], dataset, device))


if __name__ == "__main__":
    import sys

    print("=== مترجم انگلیسی به فارسی با Transformer ===")
    print("1. آموزش مدل")
    print("2. ترجمه تعاملی")
    print("3. ترجمه یک جمله")

    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        mode = input("\nلطفا گزینه مورد نظر را انتخاب کنید (1/2/3): ").strip()

    if mode == "1" or mode == "train":
        print("\nشروع آموزش مدل...")
        train_model()

    elif mode == "2" or mode == "interactive":
        interactive_translate()

    elif mode == "3" or mode == "single":
        if len(sys.argv) > 2:
            sentence = " ".join(sys.argv[2:])
        else:
            sentence = input("جمله انگلیسی را وارد کنید: ").strip()

        if sentence:
            print(f"ترجمه: {translate_single_sentence(sentence)}")
        else:
            print("جمله‌ای وارد نشد.")

    else:
        print("گزینه نامعتبر. لطفا 1، 2 یا 3 را انتخاب کنید.")
